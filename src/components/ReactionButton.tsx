'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { SmilePlus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import ReactionPicker, { reactionOptions } from './ReactionPicker';

interface ReactionData {
  type: string;
  count: number;
  userReacted: boolean;
}

interface ReactionButtonProps {
  checkinId: string;
  initialReactions?: ReactionData[];
  initialUserReactions?: string[];
  className?: string;
}

export default function ReactionButton({
  checkinId,
  initialReactions = [],
  initialUserReactions = [],
  className = '',
}: ReactionButtonProps) {
  const [reactions, setReactions] = useState<ReactionData[]>(initialReactions);
  const [userReactions, setUserReactions] =
    useState<string[]>(initialUserReactions);

    console.log('reaction', reactions)
    console.log('userReactions', userReactions)
  const [isPickerOpen, setIsPickerOpen] = useState(false);
  const [pendingActions, setPendingActions] = useState<Set<string>>(new Set());

  // Fetch reactions data (only when needed, e.g., after user interaction)
  const fetchReactions = async () => {
    try {
      const response = await fetch(`/api/reactions?checkinId=${checkinId}`);
      if (response.ok) {
        const data = await response.json();
        setReactions(data.reactions || []);
        setUserReactions(data.userReactions || []);
      }
    } catch (error) {
      console.error('Failed to fetch reactions:', error);
    }
  };

  // Initialize with provided data, only fetch if no initial data provided
  useEffect(() => {
    if (initialReactions.length === 0 && initialUserReactions.length === 0) {
      fetchReactions();
    }
  }, [checkinId]);

  const handleReactionSelect = async (reactionType: string) => {
    if (pendingActions.has(reactionType)) return;

    const isAlreadyReacted = userReactions.includes(reactionType);

    // IMMEDIATE UI UPDATE - No delay, instant feedback
    if (isAlreadyReacted) {
      // Remove reaction optimistically
      setUserReactions((prev) => prev.filter((r) => r !== reactionType));
      setReactions((prev) => {
        const updated = [...prev];
        const existingIndex = updated.findIndex((r) => r.type === reactionType);
        if (existingIndex !== -1) {
          updated[existingIndex].count = Math.max(
            0,
            updated[existingIndex].count - 1
          );
          updated[existingIndex].userReacted = false;
          // Keep the reaction visible even at 0 count if other users have reacted
          // Only remove if count is 0 and no other users have this reaction
          if (updated[existingIndex].count === 0) {
            // Check if we should remove it completely or keep it visible
            const shouldKeepVisible = false; // We'll keep it for now to show 0 count
            if (!shouldKeepVisible) {
              updated.splice(existingIndex, 1);
            }
          }
        }
        return updated;
      });
    } else {
      // Add reaction optimistically
      setUserReactions((prev) => [...prev, reactionType]);
      setReactions((prev) => {
        const updated = [...prev];
        const existingIndex = updated.findIndex((r) => r.type === reactionType);
        if (existingIndex !== -1) {
          updated[existingIndex].count += 1;
          updated[existingIndex].userReacted = true;
        } else {
          // Create new reaction entry
          updated.push({
            type: reactionType,
            count: 1,
            userReacted: true,
          });
        }
        return updated;
      });
    }

    // Add to pending actions for visual feedback
    setPendingActions((prev) => new Set(prev).add(reactionType));

    // Sync with API in background (non-blocking)
    syncReactionWithAPI(reactionType, isAlreadyReacted);
  };

  // Separate async function for API sync - runs in background
  const syncReactionWithAPI = async (reactionType: string, wasAlreadyReacted: boolean) => {
    try {
      if (wasAlreadyReacted) {
        // Remove reaction from server
        const response = await fetch(
          `/api/reactions?checkinId=${checkinId}&reactionType=${reactionType}`,
          {
            method: 'DELETE',
          }
        );

        if (!response.ok) {
          // Revert optimistic update on failure
          setUserReactions((prev) => [...prev, reactionType]);
          setReactions((prev) => {
            const updated = [...prev];
            const existingIndex = updated.findIndex(
              (r) => r.type === reactionType
            );
            if (existingIndex !== -1) {
              updated[existingIndex].count += 1;
              updated[existingIndex].userReacted = true;
            } else {
              updated.push({
                type: reactionType,
                count: 1,
                userReacted: true,
              });
            }
            return updated;
          });
          toast.error('Failed to remove reaction - reverted');
        }
      } else {
        // Add reaction to server
        const response = await fetch('/api/reactions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            checkinId,
            reactionType,
          }),
        });

        if (!response.ok) {
          // Revert optimistic update on failure
          setUserReactions((prev) => prev.filter((r) => r !== reactionType));
          setReactions((prev) => {
            const updated = [...prev];
            const existingIndex = updated.findIndex(
              (r) => r.type === reactionType
            );
            if (existingIndex !== -1) {
              updated[existingIndex].count = Math.max(
                0,
                updated[existingIndex].count - 1
              );
              updated[existingIndex].userReacted = false;
              if (updated[existingIndex].count === 0) {
                updated.splice(existingIndex, 1);
              }
            }
            return updated;
          });
          toast.error('Failed to add reaction - reverted');
        }
      }
    } catch (error) {
      console.error('Failed to sync reaction:', error);
      // Revert optimistic update on network error
      if (wasAlreadyReacted) {
        setUserReactions((prev) => [...prev, reactionType]);
        setReactions((prev) => {
          const updated = [...prev];
          const existingIndex = updated.findIndex((r) => r.type === reactionType);
          if (existingIndex !== -1) {
            updated[existingIndex].count += 1;
            updated[existingIndex].userReacted = true;
          } else {
            updated.push({
              type: reactionType,
              count: 1,
              userReacted: true,
            });
          }
          return updated;
        });
      } else {
        setUserReactions((prev) => prev.filter((r) => r !== reactionType));
        setReactions((prev) => {
          const updated = [...prev];
          const existingIndex = updated.findIndex((r) => r.type === reactionType);
          if (existingIndex !== -1) {
            updated[existingIndex].count = Math.max(0, updated[existingIndex].count - 1);
            updated[existingIndex].userReacted = false;
            if (updated[existingIndex].count === 0) {
              updated.splice(existingIndex, 1);
            }
          }
          return updated;
        });
      }
      toast.error('Network error - reaction reverted');
    } finally {
      // Remove from pending actions after API call completes
      setPendingActions((prev) => {
        const updated = new Set(prev);
        updated.delete(reactionType);
        return updated;
      });
    }
  };

  console.log('userReactions', userReactions)

  return (
    <div className={`relative ${className}`}>
      {/* Show all available reactions with counts */}
      <div className='flex items-center gap-1'>
        {/* Show all reaction types that have been used, or all available types if none have been used */}
        {(reactions.length > 0 ? reactions : reactionOptions.map(option => ({ type: option.type, count: 0, userReacted: false })))
          .map((reactionData) => {
            const reactionOption = reactionOptions.find(
              (r) => r.type === reactionData.type
            );
            const userHasReacted = userReactions.includes(reactionData.type);
            const isPending = pendingActions.has(reactionData.type);
            const shouldShow = reactionData.count > 0 || userHasReacted;

            // Only show reactions that have counts or user has reacted to
            if (!shouldShow) return null;

            return (
              <motion.div
                key={reactionData.type}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ duration: 0.1 }}
              >
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => handleReactionSelect(reactionData.type)}
                  disabled={isPending}
                  className={`
                    h-8 px-2 rounded-full transition-all duration-200 cursor-pointer
                    ${userHasReacted
                      ? 'bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50 text-blue-600 dark:text-blue-400 ring-1 ring-blue-300 dark:ring-blue-700'
                      : 'bg-slate-100 dark:bg-slate-800 hover:bg-slate-200 dark:hover:bg-slate-700 text-slate-600 dark:text-slate-400'
                    }
                    ${isPending ? 'opacity-50' : ''}
                  `}
                >
                  <motion.div
                    className='flex items-center gap-1'
                    animate={{
                      scale: isPending ? 0.95 : 1,
                      opacity: isPending ? 0.7 : 1,
                    }}
                    transition={{ duration: 0.1 }}
                  >
                    <motion.span
                      initial={{ scale: 0, rotate: -180 }}
                      animate={{ scale: 1, rotate: 0 }}
                      transition={{
                        type: 'spring',
                        stiffness: 400,
                        damping: 25,
                      }}
                      className='text-sm'
                    >
                      {reactionOption?.emoji}
                    </motion.span>

                    <AnimatePresence mode='wait'>
                      <motion.span
                        key={reactionData.count}
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        transition={{ duration: 0.15 }}
                        className='text-xs font-medium'
                      >
                        {reactionData.count}
                      </motion.span>
                    </AnimatePresence>
                  </motion.div>
                </Button>
              </motion.div>
            );
          })}

        {/* Show all available reactions if no reactions exist yet */}
        {reactions.length === 0 && (
          <>
            {reactionOptions.map((option) => {
              const userHasReacted = userReactions.includes(option.type);
              const isPending = pendingActions.has(option.type);

              return (
                <motion.div
                  key={option.type}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ duration: 0.1 }}
                >
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => handleReactionSelect(option.type)}
                    disabled={isPending}
                    className={`
                      h-8 px-2 rounded-full transition-all duration-200 cursor-pointer
                      ${userHasReacted
                        ? 'bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50 text-blue-600 dark:text-blue-400 ring-1 ring-blue-300 dark:ring-blue-700'
                        : 'bg-slate-100 dark:bg-slate-800 hover:bg-slate-200 dark:hover:bg-slate-700 text-slate-600 dark:text-slate-400'
                      }
                      ${isPending ? 'opacity-50' : ''}
                    `}
                  >
                    <motion.div
                      className='flex items-center gap-1'
                      animate={{
                        scale: isPending ? 0.95 : 1,
                        opacity: isPending ? 0.7 : 1,
                      }}
                      transition={{ duration: 0.1 }}
                    >
                      <motion.span
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{ scale: 1, rotate: 0 }}
                        transition={{
                          type: 'spring',
                          stiffness: 400,
                          damping: 25,
                        }}
                        className='text-sm'
                      >
                        {option.emoji}
                      </motion.span>

                      <AnimatePresence mode='wait'>
                        <motion.span
                          key={0}
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 10 }}
                          transition={{ duration: 0.15 }}
                          className='text-xs font-medium'
                        >
                          0
                        </motion.span>
                      </AnimatePresence>
                    </motion.div>
                  </Button>
                </motion.div>
              );
            })}
          </>
        )}

        {/* Add reaction button */}
        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          transition={{ duration: 0.1 }}
        >
          <Button
            variant='ghost'
            size='sm'
            onClick={() => setIsPickerOpen(!isPickerOpen)}
            className=' p-1 h-7 w-7 rounded-full hover:bg-slate-100 dark:hover:bg-slate-700 cursor-pointer flex gap-1'
          >
            <SmilePlus className='h-6 w-6' />
          </Button>
        </motion.div>
      </div>

      <ReactionPicker
        isOpen={isPickerOpen}
        onSelect={handleReactionSelect}
        onClose={() => setIsPickerOpen(false)}
        currentReaction={userReactions}
      />

      {/* Reaction summary */}
      {/* <AnimatePresence>
        {reactions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 5, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 5, scale: 0.9 }}
            transition={{ duration: 0.2 }}
            className="absolute -bottom-6 left-0 flex items-center gap-1 text-xs text-slate-600 dark:text-slate-400"
          >
            {reactions.slice(0, 3).map((reaction, index) => {
              const option = reactionOptions.find(r => r.type === reaction.type);
              return (
                <motion.div
                  key={reaction.type}
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.5 }}
                  transition={{
                    delay: index * 0.05,
                    type: "spring",
                    stiffness: 300,
                    damping: 25
                  }}
                  whileHover={{ scale: 1.1 }}
                  className="flex items-center gap-0.5 cursor-default"
                >
                  <motion.span
                    animate={{
                      scale: reaction.userReacted ? [1, 1.2, 1] : 1
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    {option?.emoji}
                  </motion.span>
                  <motion.span
                    key={reaction.count}
                    initial={{ scale: 0.8 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    {reaction.count}
                  </motion.span>
                </motion.div>
              );
            })}
            {reactions.length > 3 && (
              <motion.span
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-slate-500"
              >
                +{reactions.length - 3}
              </motion.span>
            )}
          </motion.div>
        )}
      </AnimatePresence> */}
    </div>
  );
}
